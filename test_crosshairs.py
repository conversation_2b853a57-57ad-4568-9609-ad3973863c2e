#!/usr/bin/env python3
"""
Test script to demonstrate crosshair functionality in the minimap viewer.

This script shows how to use the new crosshair features:
1. Right-click to place crosshairs at any position
2. Crosshairs persist when switching between floors
3. Only one set of crosshairs per floor
4. Crosshairs are drawn on top of the minimap content
"""

import sys
from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtCore import QTimer
from minimap_viewer import MinimapViewerWindow

def show_instructions():
    """Show instructions for using the crosshair functionality."""
    instructions = """
Global Crosshair Functionality Test

Instructions:
1. Right-click anywhere on the minimap to place GLOBAL crosshairs
2. The crosshairs will appear as super thin red lines + center square outline
3. Switch between floors - crosshairs automatically appear on ALL floors at the same position
4. Right-click a new position to move the crosshairs globally
5. Press Ctrl+C to clear crosshairs from all floors

Features:
- ONE global crosshair that appears on ALL floors
- Super thin (0.25px) lines for minimal visual interference
- 1x1 pixel square outline at center (no fill - doesn't cover the pixel)
- Pixel-perfect snapping to exact pixel centers for game coordinate alignment
- Crosshairs automatically appear when switching floors (no recreation needed)
- Crosshairs are drawn on top of all minimap content
- Crosshairs persist when zooming and panning
- Up to 20x zoom for pixel-precise selection

Try it out!
"""
    
    msg = QMessageBox()
    msg.setWindowTitle("Crosshair Test Instructions")
    msg.setText(instructions)
    msg.setIcon(QMessageBox.Icon.Information)
    msg.exec()

def main():
    """Main function to run the crosshair test."""
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("FiendishFinder Crosshair Test")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("FiendishFinder")
    
    # Create and show main window
    window = MinimapViewerWindow()
    window.setWindowTitle("FiendishFinder - Crosshair Test")
    window.show()
    
    # Show instructions after a short delay
    QTimer.singleShot(1000, show_instructions)
    
    # Run application
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
