#!/usr/bin/env python3
"""
Test script to demonstrate crosshair functionality in the minimap viewer.

This script shows how to use the new crosshair features:
1. Right-click to place crosshairs at any position
2. Crosshairs persist when switching between floors
3. Only one set of crosshairs per floor
4. Crosshairs are drawn on top of the minimap content
"""

import sys
from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtCore import QTimer
from minimap_viewer import MinimapViewerWindow

def show_instructions():
    """Show instructions for using the crosshair functionality."""
    instructions = """
Crosshair Functionality Test

Instructions:
1. Right-click anywhere on the minimap to place crosshairs
2. The crosshairs will appear as red lines extending across the entire minimap
3. Switch between floors using the floor controls - crosshairs are preserved per floor
4. Right-click a new position to move the crosshairs
5. Each floor can have its own crosshair position

Features:
- Crosshairs are drawn on top of all minimap content
- Crosshairs persist when zooming and panning
- Each floor remembers its own crosshair position
- Only one set of crosshairs per floor (new ones replace old ones)

Try it out!
"""
    
    msg = QMessageBox()
    msg.setWindowTitle("Crosshair Test Instructions")
    msg.setText(instructions)
    msg.setIcon(QMessageBox.Icon.Information)
    msg.exec()

def main():
    """Main function to run the crosshair test."""
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("FiendishFinder Crosshair Test")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("FiendishFinder")
    
    # Create and show main window
    window = MinimapViewerWindow()
    window.setWindowTitle("FiendishFinder - Crosshair Test")
    window.show()
    
    # Show instructions after a short delay
    QTimer.singleShot(1000, show_instructions)
    
    # Run application
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
