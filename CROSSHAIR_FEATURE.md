# Crosshair Feature Documentation

## Overview

The minimap viewer now includes a crosshair functionality that allows users to place visual markers at specific coordinates on the minimap. This feature enhances navigation and position marking capabilities.

## Features

### ✅ Crosshair Placement
- **Right-click** anywhere on the minimap to place crosshairs at that position
- Crosshairs consist of:
  - A **horizontal line** extending from the left edge to the right edge of the minimap
  - A **vertical line** extending from the top edge to the bottom edge of the minimap
  - Both lines intersect at the clicked position

### ✅ Visual Design
- **Color**: Bright red (`RGB(255, 0, 0)`) with slight transparency for visibility
- **Width**: 2 pixels for clear visibility without being obtrusive
- **Z-Index**: 1000 (drawn on top of all minimap content)
- **Style**: Solid lines that span the entire minimap area

### ✅ Persistence Across Floors
- Each floor can have its own crosshair position
- Crosshairs are automatically saved when switching floors
- Crosshairs are automatically restored when returning to a floor
- Only one set of crosshairs per floor (new crosshairs replace existing ones)

### ✅ Interactive Behavior
- **Placement**: Right-click to place new crosshairs
- **Replacement**: Right-click a new position to move crosshairs
- **Clearing**: Press `Ctrl+C` to clear crosshairs from the current floor
- **Zoom/Pan**: Crosshairs remain fixed to their scene coordinates during zoom and pan operations

## Usage Instructions

### Basic Usage
1. **Place Crosshairs**: Right-click anywhere on the minimap
2. **Move Crosshairs**: Right-click a new position (replaces previous crosshairs)
3. **Clear Crosshairs**: Press `Ctrl+C` to remove crosshairs from current floor

### Multi-Floor Usage
1. Place crosshairs on Floor A by right-clicking
2. Switch to Floor B using floor controls
3. Place different crosshairs on Floor B by right-clicking
4. Switch back to Floor A - crosshairs are automatically restored
5. Each floor maintains its own crosshair position independently

## Technical Implementation

### Key Components

#### MinimapGraphicsView Class
- `crosshair_horizontal`: QGraphicsLineItem for horizontal line
- `crosshair_vertical`: QGraphicsLineItem for vertical line
- `crosshair_position`: QPointF storing current crosshair scene coordinates
- `floor_crosshair_positions`: Dict mapping floor IDs to crosshair positions

#### Core Methods
- `place_crosshairs(scene_pos)`: Creates crosshairs at specified position
- `remove_crosshairs()`: Removes current crosshairs from scene
- `restore_crosshairs(floor_id)`: Restores saved crosshairs for a floor
- `save_crosshair_position(floor_id)`: Saves current crosshair position
- `clear_all_crosshairs()`: Clears crosshairs from all floors

### Event Handling
- **Mouse Events**: Right-click detection in `mousePressEvent()`
- **Keyboard Events**: `Ctrl+C` shortcut in `keyPressEvent()`
- **Floor Changes**: Automatic save/restore in `set_floor()` method

### Coordinate System
- Uses **scene coordinates** for position storage
- Crosshairs span the entire scene rectangle
- Positions are preserved across zoom/pan operations
- Coordinates are logged for debugging purposes

## Testing

### Test Script
Run `test_crosshairs.py` to test the functionality:
```bash
python test_crosshairs.py
```

### Manual Testing Checklist
- [ ] Right-click places crosshairs
- [ ] Crosshairs appear as red lines spanning full minimap
- [ ] Right-click new position moves crosshairs
- [ ] Ctrl+C clears crosshairs
- [ ] Crosshairs persist when switching floors
- [ ] Each floor has independent crosshair position
- [ ] Crosshairs remain fixed during zoom/pan
- [ ] Crosshairs are drawn on top of minimap content

## Logging

The feature includes comprehensive logging:
- `INFO`: Crosshair placement and restoration events
- `DEBUG`: Crosshair position saving events
- Coordinates are logged with 2 decimal precision
- Floor-specific operations are clearly identified

## Future Enhancements

Potential improvements for future versions:
- Multiple crosshairs per floor
- Different crosshair colors/styles
- Crosshair labels or annotations
- Export crosshair positions to file
- Import crosshair positions from file
- Crosshair opacity adjustment
- Custom crosshair shapes (circle, square, etc.)

## Compatibility

- **PyQt6**: Fully compatible with PyQt6 graphics framework
- **Camera System**: Integrates seamlessly with existing camera position preservation
- **Floor System**: Works with all floor navigation features
- **Zoom/Pan**: Compatible with all existing zoom and pan operations
